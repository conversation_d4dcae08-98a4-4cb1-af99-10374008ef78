// TOEFL iBT Listening Test Application
// JSON-driven version

class TOEFLListeningTest {
    constructor() {
        this.testConfig = null;
        this.currentPassageIndex = 0;
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.timerInterval = null;
        this.totalSeconds = 0;
        
        // DOM elements
        this.loadingScreen = document.getElementById('loading-screen');
        this.errorScreen = document.getElementById('error-screen');
        this.appContainer = document.getElementById('app-container');
        this.startScreen = document.getElementById('start-screen');
        this.audioPlayerScreen = document.getElementById('audio-player-screen');
        this.questionScreen = document.getElementById('question-screen');
        this.resultsScreen = document.getElementById('results-screen');
        
        this.audioElement = document.getElementById('audio-element');
        this.timerDisplay = document.getElementById('timer');
        
        // Initialize the application
        this.init();
    }

    async init() {
        try {
            await this.loadTestConfig();
            this.setupUI();
            this.setupEventListeners();
            this.showStartScreen();
        } catch (error) {
            this.showError(error.message);
        }
    }

    async loadTestConfig(configPath = 'config/test-config.json') {
        try {
            const response = await fetch(configPath);
            if (!response.ok) {
                throw new Error(`Failed to load config: ${response.status} ${response.statusText}`);
            }
            this.testConfig = await response.json();
            
            // Validate required fields
            if (!this.testConfig.testInfo || !this.testConfig.passages || !this.testConfig.scoring) {
                throw new Error('Invalid test configuration: missing required sections');
            }
            
            // Set timer duration
            this.totalSeconds = this.testConfig.testInfo.duration || 2160; // Default 36 minutes
            
        } catch (error) {
            console.error('Error loading test config:', error);
            throw new Error(`Could not load test configuration: ${error.message}`);
        }
    }

    setupUI() {
        // Set test title and logo
        const testTitle = document.getElementById('test-title');
        const logoImg = document.getElementById('logo-img');
        
        testTitle.textContent = this.testConfig.testInfo.title;
        logoImg.src = this.testConfig.testInfo.logo;
        
        // Setup instructions
        const instructionsContainer = document.getElementById('instructions-container');
        instructionsContainer.innerHTML = '';
        
        // Add main description
        const mainDesc = document.createElement('p');
        mainDesc.className = 'mb-6';
        mainDesc.textContent = this.testConfig.testInfo.instructions[0];
        instructionsContainer.appendChild(mainDesc);
        
        // Add instruction list
        const instructionsList = document.createElement('ul');
        instructionsList.className = 'list-disc list-inside space-y-2';
        
        for (let i = 1; i < this.testConfig.testInfo.instructions.length; i++) {
            const listItem = document.createElement('li');
            listItem.textContent = this.testConfig.testInfo.instructions[i];
            if (i === this.testConfig.testInfo.instructions.length - 1) {
                listItem.className = 'font-semibold text-amber-700';
            }
            instructionsList.appendChild(listItem);
        }
        
        instructionsContainer.appendChild(instructionsList);
        
        // Update timer display
        this.updateTimerDisplay();
    }

    setupEventListeners() {
        // Main navigation buttons
        document.getElementById('start-btn').addEventListener('click', () => this.startTest());
        document.getElementById('next-btn').addEventListener('click', () => this.handleNextClick());
        document.getElementById('restart-btn').addEventListener('click', () => this.resetTest());
        document.getElementById('print-report-btn').addEventListener('click', () => window.print());
        document.getElementById('retry-btn').addEventListener('click', () => this.init());
        document.getElementById('load-config-btn').addEventListener('click', () => this.loadSelectedConfig());

        // Audio controls
        document.getElementById('play-pause-btn').addEventListener('click', () => this.togglePlayPause());
        document.getElementById('skip-back-btn').addEventListener('click', () => this.skipBackward());
        document.getElementById('skip-forward-btn').addEventListener('click', () => this.skipForward());
        document.getElementById('skip-audio-btn').addEventListener('click', () => this.skipAudio());

        // Audio element events
        this.audioElement.addEventListener('timeupdate', () => this.updateProgressBar());
        this.audioElement.addEventListener('loadedmetadata', () => this.updateAudioDuration());
        this.audioElement.addEventListener('ended', () => this.showQuestionScreen());

        // Progress bar functionality
        this.setupProgressBarEvents();
    }

    setupProgressBarEvents() {
        const progressContainer = document.getElementById('progress-container');
        const progressHandle = document.getElementById('progress-handle');
        let isDragging = false;

        progressContainer.addEventListener('click', (e) => {
            if (!isDragging) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                this.seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        progressHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                this.seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events for mobile
        progressHandle.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging && e.touches.length > 0) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((e.touches[0].clientX - rect.left) / rect.width) * 100;
                this.seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    showError(message) {
        this.loadingScreen.classList.add('hidden');
        this.appContainer.classList.add('hidden');
        this.errorScreen.classList.remove('hidden');
        document.getElementById('error-message-text').textContent = message;
    }

    showStartScreen() {
        this.loadingScreen.classList.add('hidden');
        this.errorScreen.classList.add('hidden');
        this.appContainer.classList.remove('hidden');
        this.startScreen.classList.remove('hidden');
        this.audioPlayerScreen.classList.add('hidden');
        this.questionScreen.classList.add('hidden');
        this.resultsScreen.classList.add('hidden');
    }

    startTest() {
        this.startTimer();
        this.startPassage();
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            this.totalSeconds--;
            this.updateTimerDisplay();
            if (this.totalSeconds <= 0) {
                clearInterval(this.timerInterval);
                this.showResults();
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const minutes = Math.floor(this.totalSeconds / 60);
        const seconds = this.totalSeconds % 60;
        this.timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    startPassage() {
        this.startScreen.classList.add('hidden');
        this.questionScreen.classList.add('hidden');
        this.audioPlayerScreen.classList.remove('hidden');

        const passage = this.testConfig.passages[this.currentPassageIndex];
        document.getElementById('passage-title').textContent = `Playing: ${passage.title}`;
        document.getElementById('passage-instruction').textContent = passage.instruction;
        
        // Reset progress bar
        document.getElementById('progress-bar').style.width = '0%';
        
        this.playAudio(passage.audioFile);
    }

    playAudio(audioFile) {
        this.audioElement.src = audioFile;
        this.audioElement.play().catch(e => {
            console.error("Audio play failed:", e);
            // If audio fails, show question screen after a short delay
            setTimeout(() => this.showQuestionScreen(), 2000);
        });
    }

    // Audio control methods
    togglePlayPause() {
        const playPauseText = document.getElementById('play-pause-text');
        const playIcon = document.getElementById('play-icon');
        
        if (this.audioElement.paused) {
            this.audioElement.play();
            playPauseText.textContent = 'Pause';
            playIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />`;
        } else {
            this.audioElement.pause();
            playPauseText.textContent = 'Play';
            playIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V6a2 2 0 012-2z" />`;
        }
    }

    skipBackward() {
        this.audioElement.currentTime = Math.max(0, this.audioElement.currentTime - 10);
        this.updateProgressBar();
    }

    skipForward() {
        if (this.audioElement.duration) {
            this.audioElement.currentTime = Math.min(this.audioElement.duration, this.audioElement.currentTime + 10);
            this.updateProgressBar();
        }
    }

    skipAudio() {
        this.audioElement.currentTime = this.audioElement.duration;
        this.showQuestionScreen();
    }

    updateProgressBar() {
        if (!this.audioElement.duration) return;
        const percentage = (this.audioElement.currentTime / this.audioElement.duration) * 100;
        document.getElementById('progress-bar').style.width = `${percentage}%`;
        document.getElementById('current-time').textContent = this.formatTime(this.audioElement.currentTime);
    }

    updateAudioDuration() {
        if (this.audioElement.duration) {
            document.getElementById('total-duration').textContent = this.formatTime(this.audioElement.duration);
        }
    }

    seekToPosition(percentage) {
        if (this.audioElement.duration) {
            const newTime = (percentage / 100) * this.audioElement.duration;
            this.audioElement.currentTime = newTime;
            this.updateProgressBar();
        }
    }

    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    showQuestionScreen() {
        this.audioPlayerScreen.classList.add('hidden');
        this.questionScreen.classList.remove('hidden');
        this.loadQuestion();
    }

    loadQuestion() {
        const nextBtn = document.getElementById('next-btn');
        nextBtn.disabled = true;

        const passage = this.testConfig.passages[this.currentPassageIndex];
        const question = passage.questions[this.currentQuestionIndex];

        document.getElementById('question-passage-title').textContent = passage.title;
        const totalQuestionsInPassage = passage.questions.length;
        document.getElementById('question-counter').textContent = `Question ${this.currentQuestionIndex + 1} of ${totalQuestionsInPassage}`;

        document.getElementById('question-text').innerHTML = question.text.replace(/\n/g, '<br>');

        const questionOptions = document.getElementById('question-options');
        questionOptions.innerHTML = '';

        question.options.forEach(optionText => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'question-option border-2 border-slate-300 p-4 rounded-lg cursor-pointer';
            optionDiv.textContent = optionText;
            optionDiv.addEventListener('click', () => this.selectOption(optionDiv, question.type === 'multiple'));
            questionOptions.appendChild(optionDiv);
        });
    }

    selectOption(selectedDiv, isMultiple) {
        const questionOptions = document.getElementById('question-options');
        const nextBtn = document.getElementById('next-btn');

        if (isMultiple) {
            selectedDiv.classList.toggle('selected');
        } else {
            // Deselect any previously selected option
            const allOptions = questionOptions.querySelectorAll('.question-option');
            allOptions.forEach(opt => opt.classList.remove('selected'));
            // Select the new one
            selectedDiv.classList.add('selected');
        }

        // Enable next button if at least one option is selected
        const selectedCount = questionOptions.querySelectorAll('.selected').length;
        nextBtn.disabled = selectedCount === 0;
    }

    handleNextClick() {
        // Record answer
        const selectedDivs = document.getElementById('question-options').querySelectorAll('.selected');
        const answers = Array.from(selectedDivs).map(div => div.textContent);
        this.userAnswers.push(answers);

        // Move to next question or passage
        this.currentQuestionIndex++;
        if (this.currentQuestionIndex < this.testConfig.passages[this.currentPassageIndex].questions.length) {
            this.loadQuestion();
        } else {
            this.currentPassageIndex++;
            this.currentQuestionIndex = 0;
            if (this.currentPassageIndex < this.testConfig.passages.length) {
                this.startPassage();
            } else {
                this.showResults();
            }
        }
    }

    showResults() {
        clearInterval(this.timerInterval);
        this.audioElement.pause();
        this.audioElement.currentTime = 0;
        this.audioPlayerScreen.classList.add('hidden');
        this.questionScreen.classList.add('hidden');
        this.resultsScreen.classList.remove('hidden');

        this.generateTestReport();
    }

    generateTestReport() {
        let correctCount = 0;
        let totalQuestionCount = 0;
        let answerIndex = 0;
        let passageResults = [];

        // Analyze each passage
        this.testConfig.passages.forEach((passage, passageIndex) => {
            let passageCorrect = 0;
            let passageTotal = passage.questions.length;
            let questionDetails = [];

            passage.questions.forEach((question, questionIndex) => {
                totalQuestionCount++;
                const userAnswer = this.userAnswers[answerIndex] || [];
                const correctAnswer = Array.isArray(question.correctAnswer) ? question.correctAnswer : [question.correctAnswer];

                // For multiple choice, all correct answers must be selected and no incorrect ones.
                const isCorrect = userAnswer.length === correctAnswer.length && userAnswer.every(ans => correctAnswer.includes(ans));

                if (isCorrect) {
                    correctCount++;
                    passageCorrect++;
                }

                questionDetails.push({
                    questionNumber: questionIndex + 1,
                    question: question.text,
                    userAnswer: userAnswer,
                    correctAnswer: correctAnswer,
                    isCorrect: isCorrect,
                    isMultiple: question.type === 'multiple',
                    explanation: question.explanation || ''
                });

                answerIndex++;
            });

            passageResults.push({
                title: passage.title,
                type: passage.type,
                correct: passageCorrect,
                total: passageTotal,
                percentage: Math.round((passageCorrect / passageTotal) * 100),
                questions: questionDetails
            });
        });

        // Update overall score
        const overallPercentage = Math.round((correctCount / totalQuestionCount) * 100);
        document.getElementById('score').textContent = correctCount;
        document.getElementById('total-questions').textContent = totalQuestionCount;
        document.getElementById('percentage-score').textContent = overallPercentage;

        // Generate detailed reports
        this.generatePassageSummaries(passageResults);
        this.generateQuestionAnalysis(passageResults);
        this.generateOverallAnalysis(passageResults, correctCount, totalQuestionCount, overallPercentage);
    }

    generatePassageSummaries(passageResults) {
        const passageSummaryContainer = document.getElementById('passage-summary');
        passageSummaryContainer.innerHTML = '';

        passageResults.forEach((passage, index) => {
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'bg-slate-50 rounded-lg p-4';

            const statusColor = passage.percentage >= 70 ? 'text-green-600' : passage.percentage >= 50 ? 'text-yellow-600' : 'text-red-600';
            const statusBg = passage.percentage >= 70 ? 'bg-green-100' : passage.percentage >= 50 ? 'bg-yellow-100' : 'bg-red-100';

            summaryDiv.innerHTML = `
                <h4 class="font-semibold text-slate-800 mb-2">${passage.title}</h4>
                <p class="text-sm text-slate-600 mb-2">${passage.type}</p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold ${statusColor}">${passage.correct}/${passage.total}</span>
                    <span class="px-2 py-1 rounded-full text-sm font-medium ${statusColor} ${statusBg}">${passage.percentage}%</span>
                </div>
            `;

            passageSummaryContainer.appendChild(summaryDiv);
        });
    }

    generateQuestionAnalysis(passageResults) {
        const questionAnalysisContainer = document.getElementById('question-analysis');
        questionAnalysisContainer.innerHTML = '';

        passageResults.forEach((passage, passageIndex) => {
            const passageDiv = document.createElement('div');
            passageDiv.className = 'mb-6 border-b border-slate-200 pb-4';

            passageDiv.innerHTML = `<h4 class="font-semibold text-slate-800 mb-3">${passage.title}</h4>`;

            passage.questions.forEach((question, questionIndex) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = `mb-4 p-4 rounded-lg border-l-4 ${question.isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`;

                const statusIcon = question.isCorrect ?
                    '<svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                    '<svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';

                questionDiv.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 mt-1">${statusIcon}</div>
                        <div class="flex-grow">
                            <p class="font-medium text-slate-800 mb-2">Question ${question.questionNumber}: ${question.question}</p>
                            <div class="text-sm">
                                <p class="mb-1"><span class="font-medium">Your answer:</span> ${question.userAnswer.length > 0 ? question.userAnswer.join(', ') : 'No answer selected'}</p>
                                <p class="mb-1"><span class="font-medium">Correct answer:</span> ${question.correctAnswer.join(', ')}</p>
                                ${question.isMultiple ? '<p class="text-slate-600 mt-1"><em>Multiple choice question</em></p>' : ''}
                                ${question.explanation ? `<p class="text-slate-600 mt-2 italic">${question.explanation}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;

                passageDiv.appendChild(questionDiv);
            });

            questionAnalysisContainer.appendChild(passageDiv);
        });
    }

    generateOverallAnalysis(passageResults, correctCount, totalQuestionCount, overallPercentage) {
        const conversationResults = passageResults.filter(p => p.type === 'Conversation');
        const lectureResults = passageResults.filter(p => p.type === 'Lecture');

        const conversationCorrect = conversationResults.reduce((sum, p) => sum + p.correct, 0);
        const conversationTotal = conversationResults.reduce((sum, p) => sum + p.total, 0);
        const lectureCorrect = lectureResults.reduce((sum, p) => sum + p.correct, 0);
        const lectureTotal = lectureResults.reduce((sum, p) => sum + p.total, 0);

        const conversationPercentage = conversationTotal > 0 ? Math.round((conversationCorrect / conversationTotal) * 100) : 0;
        const lecturePercentage = lectureTotal > 0 ? Math.round((lectureCorrect / lectureTotal) * 100) : 0;

        // Get performance level from config
        let performanceLevel = 'Needs Improvement';
        let recommendations = this.testConfig.scoring.performanceLevels.needsImprovement.message;

        const levels = this.testConfig.scoring.performanceLevels;
        if (overallPercentage >= levels.excellent.min) {
            performanceLevel = 'Excellent';
            recommendations = levels.excellent.message;
        } else if (overallPercentage >= levels.good.min) {
            performanceLevel = 'Good';
            recommendations = levels.good.message;
        } else if (overallPercentage >= levels.fair.min) {
            performanceLevel = 'Fair';
            recommendations = levels.fair.message;
        }

        const overallAnalysisContainer = document.getElementById('overall-analysis');
        overallAnalysisContainer.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2">Conversations</h4>
                    <p class="text-2xl font-bold text-blue-600">${conversationCorrect}/${conversationTotal} (${conversationPercentage}%)</p>
                    <p class="text-sm text-blue-700 mt-1">Campus and academic conversations</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <h4 class="font-semibold text-purple-800 mb-2">Lectures</h4>
                    <p class="text-2xl font-bold text-purple-600">${lectureCorrect}/${lectureTotal} (${lecturePercentage}%)</p>
                    <p class="text-sm text-purple-700 mt-1">Academic lectures and discussions</p>
                </div>
            </div>

            <div class="bg-slate-50 rounded-lg p-4">
                <h4 class="font-semibold text-slate-800 mb-2">Performance Level: <span class="text-blue-600">${performanceLevel}</span></h4>
                <p class="text-slate-700 mb-3">${recommendations}</p>

                <div class="text-sm text-slate-600">
                    <p class="mb-1"><strong>Test Completion:</strong> ${totalQuestionCount} questions answered</p>
                    <p class="mb-1"><strong>Overall Accuracy:</strong> ${overallPercentage}%</p>
                    <p><strong>Areas for Focus:</strong> ${conversationPercentage < lecturePercentage ? 'Conversations' : lecturePercentage < conversationPercentage ? 'Lectures' : 'Continue balanced practice'}</p>
                </div>
            </div>
        `;
    }

    async loadSelectedConfig() {
        const configSelector = document.getElementById('config-selector');
        const selectedConfig = configSelector.value;

        try {
            this.loadingScreen.classList.remove('hidden');
            this.appContainer.classList.add('hidden');

            await this.loadTestConfig(selectedConfig);
            this.setupUI();
            this.showStartScreen();
        } catch (error) {
            this.showError(error.message);
        }
    }

    resetTest() {
        this.currentPassageIndex = 0;
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.totalSeconds = this.testConfig.testInfo.duration;
        this.updateTimerDisplay();

        this.resultsScreen.classList.add('hidden');
        this.showStartScreen();
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TOEFLListeningTest();
});
