<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOEFL iBT Listening Mock Exam</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .question-option {
            transition: all 0.2s ease-in-out;
        }
        .question-option.selected {
            background-color: #3b82f6; /* bg-blue-600 */
            color: white;
            border-color: #3b82f6; /* border-blue-600 */
        }
        .question-option:hover:not(.selected) {
            background-color: #eff6ff; /* bg-blue-50 */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9; /* slate-100 */
        }
        ::-webkit-scrollbar-thumb {
            background: #94a3b8; /* slate-400 */
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #64748b; /* slate-500 */
        }

        /* Print styles */
        @media print {
            body {
                background: white !important;
            }
            #app-container {
                box-shadow: none !important;
                max-width: none !important;
            }
            header {
                background: #1e293b !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .bg-blue-50, .bg-green-50, .bg-red-50, .bg-yellow-50, .bg-purple-50, .bg-slate-50 {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            button {
                display: none !important;
            }
            .shadow-md, .shadow-2xl {
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
            }
        }

        /* Loading spinner */
        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body class="bg-slate-100 flex items-center justify-center min-h-screen p-4">
    <!-- Audio element to play real MP3s -->
    <audio id="audio-element" class="hidden"></audio>

    <!-- Loading Screen -->
    <div id="loading-screen" class="text-center">
        <div class="spinner mx-auto mb-4"></div>
        <p class="text-slate-600">Loading test configuration...</p>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="hidden max-w-2xl mx-auto">
        <div class="error-message">
            <h2 class="text-xl font-bold mb-2">Error Loading Test</h2>
            <p id="error-message-text">Failed to load test configuration. Please check that the config file exists and is properly formatted.</p>
            <button id="retry-btn" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Retry</button>
        </div>
    </div>

    <div id="app-container" class="w-full max-w-5xl mx-auto bg-white rounded-lg shadow-2xl overflow-hidden hidden">
        
        <!-- Header -->
        <header class="bg-slate-800 text-white p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <!-- Logo -->
                <div class="w-10 h-10 flex-shrink-0">
                    <img id="logo-img" src="" alt="TOEFL Logo" class="w-10 h-10 object-contain rounded-md">
                </div>
                <h1 id="test-title" class="text-xl font-bold">TOEFL iBT Listening Section</h1>
            </div>
            <div id="timer" class="text-lg font-mono bg-slate-700 px-4 py-1 rounded-md">36:00</div>
        </header>

        <main id="main-content" class="p-6 md:p-8">
            <!-- Start Screen -->
            <div id="start-screen">
                <h2 class="text-2xl font-bold text-slate-800 mb-4">Instructions</h2>

                <!-- Test Configuration Selector -->
                <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">Select Test Configuration</h3>
                    <select id="config-selector" class="w-full p-2 border border-blue-300 rounded-md bg-white">
                        <option value="config/test-config.json">Full TOEFL Test (36 minutes, 5 passages, 28 questions)</option>
                        <option value="config/practice-test-1.json">Practice Test 1 (30 minutes, 2 passages, 11 questions)</option>
                    </select>
                    <button id="load-config-btn" class="mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">Load Selected Test</button>
                </div>

                <div id="instructions-container" class="text-slate-600 mb-6">
                    <!-- Instructions will be loaded from JSON -->
                </div>
                <button id="start-btn" class="w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">Start Test</button>
            </div>

            <!-- Audio Player Screen -->
            <div id="audio-player-screen" class="hidden text-center">
                 <h2 id="passage-title" class="text-2xl font-bold text-slate-800 mb-4"></h2>
                 <p id="passage-instruction" class="text-slate-600 mb-6"></p>

                 <!-- Audio Controls -->
                 <div class="bg-slate-50 rounded-lg p-6 mb-6 max-w-2xl mx-auto">
                    <div class="flex items-center justify-center space-x-4 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.636 8.464a5 5 0 000 7.072m2.828 9.9a9 9 0 000-12.728" />
                        </svg>
                        <p class="text-lg font-semibold text-slate-700">Audio is playing... Please listen and take notes.</p>
                    </div>

                    <!-- Time Display -->
                    <div class="flex justify-between text-sm text-slate-600 mb-2">
                        <span id="current-time">0:00</span>
                        <span id="total-duration">0:00</span>
                    </div>

                    <!-- Progress Bar (Draggable) -->
                    <div class="relative w-full bg-slate-200 rounded-full h-3 mb-4 cursor-pointer" id="progress-container">
                        <div id="progress-bar" class="bg-blue-600 h-3 rounded-full relative" style="width: 0%">
                            <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-600 rounded-full border-2 border-white shadow-md cursor-grab active:cursor-grabbing" id="progress-handle"></div>
                        </div>
                    </div>

                    <!-- Control Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="play-pause-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg id="play-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V6a2 2 0 012-2z" />
                            </svg>
                            <span id="play-pause-text">Pause</span>
                        </button>
                        <button id="skip-back-btn" class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.334 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z" />
                            </svg>
                            <span>-10s</span>
                        </button>
                        <button id="skip-forward-btn" class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z" />
                            </svg>
                            <span>+10s</span>
                        </button>
                        <button id="skip-audio-btn" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Skip Audio</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Question Screen -->
            <div id="question-screen" class="hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="question-passage-title" class="text-lg font-semibold text-slate-700"></h3>
                    <div id="question-counter" class="text-sm font-medium text-slate-500"></div>
                </div>
                <div id="question-text" class="text-lg text-slate-800 mb-6 font-medium"></div>
                <div id="question-options" class="space-y-3"></div>
                <div class="mt-8 flex justify-end">
                    <button id="next-btn" class="bg-blue-600 text-white font-bold py-2 px-8 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-slate-400" disabled>Next</button>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="results-screen" class="hidden">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-slate-800 mb-4">TOEFL iBT Listening Test Report</h2>
                    <div class="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 inline-block">
                        <p class="text-lg text-slate-700">Overall Score</p>
                        <p class="text-5xl font-bold text-blue-600 my-2"><span id="score"></span> / <span id="total-questions"></span></p>
                        <p class="text-lg text-slate-700">(<span id="percentage-score"></span>%)</p>
                    </div>
                </div>

                <!-- Performance Summary -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Performance Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="passage-summary">
                        <!-- Passage summaries will be inserted here -->
                    </div>
                </div>

                <!-- Detailed Question Analysis -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Detailed Question Analysis</h3>
                    <div id="question-analysis">
                        <!-- Question analysis will be inserted here -->
                    </div>
                </div>

                <!-- Overall Analysis -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Overall Analysis</h3>
                    <div id="overall-analysis" class="text-slate-700">
                        <!-- Overall analysis will be inserted here -->
                    </div>
                </div>

                <div class="text-center">
                    <button id="restart-btn" class="bg-slate-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-slate-700 transition-colors mr-4">Take Again</button>
                    <button id="print-report-btn" class="bg-blue-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-blue-700 transition-colors">Print Report</button>
                </div>
            </div>

        </main>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
